{"add_bos_token": false, "add_eos_token": false, "add_prefix_space": false, "added_tokens_decoder": {"0": {"content": "<|endoftext|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "1": {"content": "<|im_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "2": {"content": "<|im_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "additional_special_tokens": [], "bos_token": "<|im_start|>", "clean_up_tokenization_spaces": false, "eos_token": "<|im_end|>", "legacy": true, "model_max_length": 32768, "pad_token": "<|endoftext|>", "sp_model_kwargs": {}, "spaces_between_special_tokens": false, "tokenizer_class": "PreTrainedTokenizerFast", "unk_token": "<|endoftext|>", "chat_template": "{% if messages[0]['role'] == 'system' %}{% set system_message = messages[0]['content'] %}{{ '<|im_start|>system\\n' + system_message + '<|im_end|>\\n' }}{% else %}{{ '<|im_start|>system\\nYou are a helpful assistant<|im_end|>\\n' }}{% endif %}{% for message in messages %}{% set content = message['content'] %}{% if message['role'] == 'user' %}{{ '<|im_start|>user\\n' + content + '<|im_end|>\\n<|im_start|>assistant\\n' }}{% elif message['role'] == 'assistant' %}{{ content + '<|im_end|>' + '\\n' }}{% endif %}{% endfor %}"}